name: NCast - Podcast App (Community)
lastModified: '2025-07-10T10:11:05Z'
thumbnailUrl: >-
  https://s3-alpha.figma.com/thumbnails/043dc3ea-47e1-4e4c-9134-e072d8ae896a?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCU3RHYTUS%2F20250710%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250710T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=e3dd57fc256afd215eda6501f8103453e8c7f838ded4fa000fc1be697c62a37b
nodes:
  - id: '46:6'
    name: Podcy - Home
    type: FRAME
    boundingBox:
      x: 1207
      'y': 24
      width: 428
      height: 926
    fills: '[{"type":"<PERSON><PERSON><PERSON>","rgba":"rgba(255, 255, 255, 1)","hex":"#ffffff"}]'
    children:
      - id: '46:30'
        name: Group 147
        type: GROUP
        boundingBox:
          x: 1555
          'y': 72
          width: 48
          height: 48
        borderRadius: 0,0,0,0
        children:
          - id: '46:28'
            name: Group 146
            type: GROUP
            boundingBox:
              x: 1555
              'y': 72
              width: 48
              height: 48
            borderRadius: 0,0,0,0
            children:
              - id: '46:27'
                name: Rectangle 1563
                type: RECTANGLE
                boundingBox:
                  x: 1555
                  'y': 72
                  width: 48
                  height: 48
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                opacity: 0.10000000149011612
                borderRadius: '24'
              - id: '46:21'
                name: Frame
                type: FRAME
                boundingBox:
                  x: 1568
                  'y': 85
                  width: 21
                  height: 21
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(255, 255, 255,
                  1)","hex":"#ffffff"}]
                children:
                  - id: '46:22'
                    name: bell-simple-fill
                    type: GROUP
                    boundingBox:
                      x: 1568
                      'y': 85
                      width: 21
                      height: 21
                    children:
                      - id: '46:23'
                        name: Rectangle 1562
                        type: VECTOR
                        boundingBox:
                          x: 1568
                          'y': 85
                          width: 21
                          height: 21
                      - id: '46:24'
                        name: Group 145
                        type: GROUP
                        boundingBox:
                          x: 1571
                          'y': 87
                          width: 15.683967590332031
                          height: 17.127904891967773
                        children:
                          - id: '46:25'
                            name: Path 9578
                            type: VECTOR
                            boundingBox:
                              x: 1571.0001220703125
                              'y': 87
                              width: 15.683967590332031
                              height: 14.492624282836914
                            fills: >-
                              [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                              1)","hex":"#1f1f1f"}]
                          - id: '46:26'
                            name: Path 9579
                            type: VECTOR
                            boundingBox:
                              x: 1575.525146484375
                              'y': 102.8101577758789
                              width: 6.621827125549316
                              height: 1.3177508115768433
                            fills: >-
                              [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                              1)","hex":"#1f1f1f"}]
          - id: '46:29'
            name: Rectangle 1564
            type: RECTANGLE
            boundingBox:
              x: 1589
              'y': 74
              width: 12
              height: 12
            fills: '[{"type":"SOLID","rgba":"rgba(255, 87, 87, 1)","hex":"#ff5757"}]'
            borderRadius: '6'
      - id: '46:42'
        name: Rectangle 1569
        type: RECTANGLE
        boundingBox:
          x: 1239
          'y': 152
          width: 364
          height: 64
        fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
        opacity: 0.07999999821186066
        borderRadius: '32'
      - id: '83:3'
        name: Group 167
        type: GROUP
        boundingBox:
          x: 1259
          'y': 172
          width: 24
          height: 24
        children:
          - id: '83:4'
            name: Rectangle 2982
            type: VECTOR
            boundingBox:
              x: 1259
              'y': 172
              width: 24
              height: 24
          - id: '83:5'
            name: Ellipse 936
            type: VECTOR
            boundingBox:
              x: 1262.055419921875
              'y': 174.3656463623047
              width: 15.89561653137207
              height: 15.89561653137207
            strokes: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
          - id: '83:6'
            name: Line 2412
            type: VECTOR
            boundingBox:
              x: 1275.62353515625
              'y': 187.93408203125
              width: 4.598419666290283
              height: 4.598419666290283
            strokes: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
      - id: '46:49'
        name: Search the podcast here...
        type: TEXT
        boundingBox:
          x: 1295
          'y': 174
          width: 196
          height: 19
        text: Search the podcast here...
        style:
          fontFamily: Public Sans
          fontWeight: 500
          fontSize: 16
          lineHeight: 1.1749999523162842em
          textAlignHorizontal: LEFT
          textAlignVertical: TOP
        fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
        opacity: 0.5
      - id: '46:51'
        name: Promoted Podcasts
        type: TEXT
        boundingBox:
          x: 1239
          'y': 248
          width: 186
          height: 24
        text: Promoted Podcasts
        style:
          fontFamily: Public Sans
          fontWeight: 700
          fontSize: 20
          lineHeight: 1.175em
          textAlignHorizontal: LEFT
          textAlignVertical: TOP
        fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
      - id: '46:85'
        name: Group 150
        type: GROUP
        boundingBox:
          x: 1239
          'y': 576
          width: 364
          height: 96
        borderRadius: 0,0,0,0
        children:
          - id: '64:188'
            name: Mask group
            type: GROUP
            boundingBox:
              x: 1239
              'y': 576
              width: 108
              height: 96
            borderRadius: 0,0,0,0
            children:
              - id: '46:58'
                name: Rectangle 1572
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 576
                  width: 108
                  height: 96
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                borderRadius: '16'
              - id: '64:187'
                name: 7338410 1
                type: RECTANGLE
                boundingBox:
                  x: 1221
                  'y': 552
                  width: 144
                  height: 144
                fills: >-
                  [{"type":"IMAGE","imageRef":"267a7057cdc760550b123419b7afac9871237057","scaleMode":"FILL"}]
          - id: '46:62'
            name: Rectangle 1573
            type: RECTANGLE
            boundingBox:
              x: 1555
              'y': 600
              width: 48
              height: 48
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            opacity: 0.10000000149011612
            borderRadius: '24'
          - id: '46:59'
            name: See Mama Be
            type: TEXT
            boundingBox:
              x: 1363
              'y': 590
              width: 104
              height: 19
            text: See Mama Be
            style:
              fontFamily: Public Sans
              fontWeight: 700
              fontSize: 16
              lineHeight: 1.1749999523162842em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
          - id: '46:60'
            name: Creative Studio
            type: TEXT
            boundingBox:
              x: 1363
              'y': 617
              width: 100
              height: 16
            text: Creative Studio
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:61'
            name: 15 min
            type: TEXT
            boundingBox:
              x: 1363
              'y': 641
              width: 42
              height: 16
            text: 15 min
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:69'
            name: Polygon 1
            type: REGULAR_POLYGON
            boundingBox:
              x: 1569.999999213195
              'y': 614.999999213195
              width: 18.000000786805003
              height: 18.000000786805003
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            borderRadius: '2'
      - id: '64:221'
        name: Group 166
        type: GROUP
        boundingBox:
          x: 1239
          'y': 816
          width: 364
          height: 96
        borderRadius: 0,0,0,0
        children:
          - id: '64:191'
            name: Mask group
            type: GROUP
            boundingBox:
              x: 1239
              'y': 816
              width: 108
              height: 96
            borderRadius: 0,0,0,0
            children:
              - id: '46:71'
                name: Rectangle 1574
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 816
                  width: 108
                  height: 96
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                borderRadius: '16'
              - id: '64:190'
                name: 7383363 1
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 811
                  width: 108
                  height: 108
                fills: >-
                  [{"type":"IMAGE","imageRef":"ed996e43d710b3e2153537c653d837d96d3c1077","scaleMode":"FILL"}]
          - id: '46:72'
            name: Rectangle 1575
            type: RECTANGLE
            boundingBox:
              x: 1555
              'y': 840
              width: 48
              height: 48
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            opacity: 0.10000000149011612
            borderRadius: '24'
          - id: '46:73'
            name: Your Time
            type: TEXT
            boundingBox:
              x: 1363
              'y': 830
              width: 76
              height: 19
            text: Your Time
            style:
              fontFamily: Public Sans
              fontWeight: 700
              fontSize: 16
              lineHeight: 1.1749999523162842em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
          - id: '46:74'
            name: Educational
            type: TEXT
            boundingBox:
              x: 1363
              'y': 857
              width: 77
              height: 16
            text: Educational
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:75'
            name: 25 min
            type: TEXT
            boundingBox:
              x: 1363
              'y': 881
              width: 45
              height: 16
            text: 25 min
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:76'
            name: Polygon 2
            type: REGULAR_POLYGON
            boundingBox:
              x: 1569.999999213195
              'y': 854.999999213195
              width: 18.000000786805003
              height: 18.000000786805003
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            borderRadius: '2'
      - id: '46:54'
        name: Trending Podcasts
        type: TEXT
        boundingBox:
          x: 1239
          'y': 528
          width: 178
          height: 24
        text: Trending Podcasts
        style:
          fontFamily: Public Sans
          fontWeight: 700
          fontSize: 20
          lineHeight: 1.175em
          textAlignHorizontal: LEFT
          textAlignVertical: TOP
        fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
      - id: '46:55'
        name: See more
        type: TEXT
        boundingBox:
          x: 1531
          'y': 531
          width: 72
          height: 19
        text: See more
        style:
          fontFamily: Public Sans
          fontWeight: 600
          fontSize: 16
          lineHeight: 1.1749999523162842em
          textAlignHorizontal: RIGHT
          textAlignVertical: TOP
        fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
        opacity: 0.699999988079071
      - id: '46:86'
        name: Group 151
        type: GROUP
        boundingBox:
          x: 1239
          'y': 696
          width: 364
          height: 96
        borderRadius: 0,0,0,0
        children:
          - id: '64:193'
            name: Mask group
            type: GROUP
            boundingBox:
              x: 1239
              'y': 696
              width: 108
              height: 96
            borderRadius: 0,0,0,0
            children:
              - id: '46:77'
                name: Rectangle 1576
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 696
                  width: 108
                  height: 96
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                borderRadius: '16'
              - id: '64:192'
                name: 7367995 1
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 690
                  width: 108
                  height: 108
                fills: >-
                  [{"type":"IMAGE","imageRef":"f93b2f165027280ef577394e4973a71796a4ad4e","scaleMode":"FILL"}]
          - id: '46:78'
            name: Rectangle 1577
            type: RECTANGLE
            boundingBox:
              x: 1555
              'y': 720
              width: 48
              height: 48
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            opacity: 0.10000000149011612
            borderRadius: '24'
          - id: '46:79'
            name: Festival on the Beach
            type: TEXT
            boundingBox:
              x: 1363
              'y': 710
              width: 163
              height: 19
            text: Festival on the Beach
            style:
              fontFamily: Public Sans
              fontWeight: 700
              fontSize: 16
              lineHeight: 1.1749999523162842em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
          - id: '46:80'
            name: Rock Electrics
            type: TEXT
            boundingBox:
              x: 1363
              'y': 737
              width: 93
              height: 16
            text: Rock Electrics
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:81'
            name: 10 min
            type: TEXT
            boundingBox:
              x: 1363
              'y': 761
              width: 42
              height: 16
            text: 10 min
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '46:82'
            name: Polygon 3
            type: REGULAR_POLYGON
            boundingBox:
              x: 1569.999999213195
              'y': 734.999999213195
              width: 18.000000786805003
              height: 18.000000786805003
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            borderRadius: '2'
      - id: '90:19'
        name: Group 179
        type: GROUP
        boundingBox:
          x: 1239
          'y': 72
          width: 221.349609375
          height: 45.69437026977539
        children:
          - id: '90:28'
            name: Ncast
            type: VECTOR
            boundingBox:
              x: 1302.884765625
              'y': 86
              width: 157.46484375
              height: 25.41796875
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
          - id: '90:21'
            name: Layer_1
            type: FRAME
            boundingBox:
              x: 1239
              'y': 72
              width: 50
              height: 45.69437026977539
            fills: '[{"type":"SOLID","rgba":"rgba(255, 255, 255, 1)","hex":"#ffffff"}]'
            children:
              - id: '90:22'
                name: Group
                type: GROUP
                boundingBox:
                  x: 1239
                  'y': 84.36079406738281
                  width: 50
                  height: 33.333580017089844
                children:
                  - id: '90:23'
                    name: Vector
                    type: VECTOR
                    boundingBox:
                      x: 1255.6656494140625
                      'y': 96.62272644042969
                      width: 16.003459930419922
                      height: 21.0716495513916
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                      1)","hex":"#4c0099"}]
                  - id: '90:24'
                    name: Vector
                    type: VECTOR
                    boundingBox:
                      x: 1256.330078125
                      'y': 84.36079406738281
                      width: 16.00273323059082
                      height: 21.071645736694336
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                      1)","hex":"#4c0099"}]
                  - id: '90:25'
                    name: Vector
                    type: VECTOR
                    boundingBox:
                      x: 1239
                      'y': 89.91675567626953
                      width: 11.111191749572754
                      height: 22.222383499145508
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                      1)","hex":"#4c0099"}]
                  - id: '90:26'
                    name: Vector
                    type: VECTOR
                    boundingBox:
                      x: 1277.8887939453125
                      'y': 89.91675567626953
                      width: 11.111194610595703
                      height: 22.222383499145508
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                      1)","hex":"#4c0099"}]
              - id: '90:27'
                name: Vector
                type: VECTOR
                boundingBox:
                  x: 1243.3056640625
                  'y': 72
                  width: 41.388736724853516
                  height: 23.47198486328125
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                  1)","hex":"#4c0099"}]
      - id: '64:182'
        name: Mask group
        type: GROUP
        boundingBox:
          x: 1239
          'y': 296
          width: 331
          height: 200
        borderRadius: 0,0,0,0
        children:
          - id: '46:50'
            name: Rectangle 1570
            type: RECTANGLE
            boundingBox:
              x: 1239
              'y': 296
              width: 331
              height: 200
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            borderRadius: '24'
          - id: '64:180'
            name: 7435440 1
            type: RECTANGLE
            boundingBox:
              x: 1227.221923828125
              'y': 260
              width: 536.824462890625
              height: 273.3912353515625
            fills: >-
              [{"type":"IMAGE","imageRef":"e46b4d84469765c453a322460062167fd2a9b0f2","scaleMode":"FILL"}]
      - id: '64:186'
        name: Mask group
        type: GROUP
        boundingBox:
          x: 1586
          'y': 296
          width: 364
          height: 200
        borderRadius: 0,0,0,0
        children:
          - id: '46:57'
            name: Rectangle 1571
            type: RECTANGLE
            boundingBox:
              x: 1586
              'y': 296
              width: 364
              height: 200
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            borderRadius: '24'
          - id: '64:184'
            name: 7435432 1
            type: RECTANGLE
            boundingBox:
              x: 1388
              'y': 258
              width: 595.241943359375
              height: 275.67724609375
            fills: >-
              [{"type":"IMAGE","imageRef":"ada29d3578ad34fa052ba2575c5df7c920a4bda4","scaleMode":"FILL"}]
      - id: '64:205'
        name: Group 155
        type: GROUP
        boundingBox:
          x: 1239
          'y': 936
          width: 364
          height: 96
        borderRadius: 0,0,0,0
        children:
          - id: '64:206'
            name: Mask group
            type: GROUP
            boundingBox:
              x: 1239
              'y': 936
              width: 108
              height: 96
            borderRadius: 0,0,0,0
            children:
              - id: '64:207'
                name: Rectangle 1576
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 936
                  width: 108
                  height: 96
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                borderRadius: '16'
              - id: '64:208'
                name: 7365696 1
                type: RECTANGLE
                boundingBox:
                  x: 1239
                  'y': 936
                  width: 108
                  height: 108
                fills: >-
                  [{"type":"IMAGE","imageRef":"f8af2f5b84940570016cb92e7697b93ee01b143e","scaleMode":"FILL"}]
          - id: '64:209'
            name: Rectangle 1577
            type: RECTANGLE
            boundingBox:
              x: 1555
              'y': 960
              width: 48
              height: 48
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            opacity: 0.10000000149011612
            borderRadius: '24'
          - id: '64:210'
            name: Music Theme
            type: TEXT
            boundingBox:
              x: 1363
              'y': 950
              width: 102
              height: 19
            text: Music Theme
            style:
              fontFamily: Public Sans
              fontWeight: 700
              fontSize: 16
              lineHeight: 1.1749999523162842em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
          - id: '64:211'
            name: Lo-fi Music
            type: TEXT
            boundingBox:
              x: 1363
              'y': 977
              width: 73
              height: 16
            text: Lo-fi Music
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '64:212'
            name: 20 min
            type: TEXT
            boundingBox:
              x: 1363
              'y': 1001
              width: 45
              height: 16
            text: 20 min
            style:
              fontFamily: Public Sans
              fontWeight: 400
              fontSize: 14
              lineHeight: 1.1749999182564872em
              textAlignHorizontal: LEFT
              textAlignVertical: TOP
            fills: '[{"type":"SOLID","rgba":"rgba(31, 31, 31, 1)","hex":"#1f1f1f"}]'
            opacity: 0.699999988079071
          - id: '64:213'
            name: Polygon 3
            type: REGULAR_POLYGON
            boundingBox:
              x: 1569.999999213195
              'y': 974.999999213195
              width: 18.000000786805003
              height: 18.000000786805003
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
            borderRadius: '2'
      - id: '76:410'
        name: Rectangle 3010
        type: RECTANGLE
        boundingBox:
          x: 1207
          'y': 792
          width: 428
          height: 158
        fills: '[{"type":"GRADIENT_LINEAR"}]'
      - id: '76:411'
        name: Group 177
        type: GROUP
        boundingBox:
          x: 1239
          'y': 846
          width: 364
          height: 72
        borderRadius: 0,0,0,0
        children:
          - id: '76:412'
            name: Rectangle 2
            type: RECTANGLE
            boundingBox:
              x: 1239
              'y': 846
              width: 364
              height: 72
            fills: >-
              [{"type":"SOLID","rgba":"rgba(76, 0, 153,
              1)","hex":"#4c0099","opacity":0.10000000149011612}]
            borderRadius: '48'
          - id: '76:413'
            name: Ellipse 939
            type: ELLIPSE
            boundingBox:
              x: 1293
              'y': 903
              width: 5
              height: 5
            fills: '[{"type":"SOLID","rgba":"rgba(76, 0, 153, 1)","hex":"#4c0099"}]'
          - id: '76:414'
            name: compass-fill
            type: GROUP
            boundingBox:
              x: 1363
              'y': 866
              width: 32
              height: 32
            opacity: 0.5
            children:
              - id: '76:415'
                name: Rectangle 1704
                type: VECTOR
                boundingBox:
                  x: 1363
                  'y': 866
                  width: 32
                  height: 32
              - id: '76:416'
                name: Path 9734
                type: VECTOR
                boundingBox:
                  x: 1366
                  'y': 869
                  width: 26.100006103515625
                  height: 26.100008010864258
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
          - id: '76:417'
            name: headphones-fill
            type: GROUP
            boundingBox:
              x: 1279
              'y': 866
              width: 32
              height: 32
            children:
              - id: '76:418'
                name: Rectangle 1889
                type: VECTOR
                boundingBox:
                  x: 1279
                  'y': 866
                  width: 32
                  height: 32
              - id: '76:419'
                name: Path 9950
                type: VECTOR
                boundingBox:
                  x: 1281.6666259765625
                  'y': 870.6666870117188
                  width: 26.282052993774414
                  height: 23.08832359313965
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(76, 0, 153,
                  1)","hex":"#4c0099"}]
          - id: '76:420'
            name: heart-fill
            type: GROUP
            boundingBox:
              x: 1447
              'y': 866
              width: 32
              height: 32
            opacity: 0.5
            children:
              - id: '76:421'
                name: Rectangle 1891
                type: VECTOR
                boundingBox:
                  x: 1447
                  'y': 866
                  width: 32
                  height: 32
              - id: '76:422'
                name: Path 9952
                type: VECTOR
                boundingBox:
                  x: 1449
                  'y': 870
                  width: 27.10333251953125
                  height: 24.090072631835938
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
          - id: '76:423'
            name: Group 152
            type: GROUP
            boundingBox:
              x: 1531
              'y': 866
              width: 32
              height: 32
            opacity: 0.5
            children:
              - id: '76:424'
                name: Rectangle 2987
                type: RECTANGLE
                boundingBox:
                  x: 1531
                  'y': 866
                  width: 32
                  height: 32
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(217, 217, 217,
                  1)","hex":"#d9d9d9","opacity":0}]
              - id: '76:425'
                name: Union
                type: BOOLEAN_OPERATION
                boundingBox:
                  x: 1535
                  'y': 871
                  width: 24
                  height: 23
                fills: >-
                  [{"type":"SOLID","rgba":"rgba(31, 31, 31,
                  1)","hex":"#1f1f1f"}]
                children:
                  - id: '76:426'
                    name: Ellipse 937
                    type: VECTOR
                    boundingBox:
                      x: 1535
                      'y': 884
                      width: 24
                      height: 10
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(151, 147, 147,
                      1)","hex":"#979393"}]
                  - id: '76:427'
                    name: Ellipse 938
                    type: ELLIPSE
                    boundingBox:
                      x: 1542
                      'y': 871
                      width: 10
                      height: 10
                    fills: >-
                      [{"type":"SOLID","rgba":"rgba(151, 147, 147,
                      1)","hex":"#979393"}]
components: {}
componentSets: {}
globalVars:
  styles: {}
